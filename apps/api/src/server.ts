import { json, urlencoded } from "body-parser";
import express, { type Express } from "express";
import morgan from "morgan";
import cors from "cors";
import { Octokit } from "octokit";
import dotenv from "dotenv";

dotenv.config();

const octokit = new Octokit({
  auth: process.env.GITHUB_TOKEN
});

async function getRepoDetail(repoUrl: string) {
  try {
    const match = repoUrl.match(/github\.com\/([^\/]+)\/([^\/]+)/);
    if (!match) throw new Error('Invalid GitHub repository URL');

    const [owner, repo] = match.slice(1);

    // Create timeout wrapper for API calls
    const withTimeout = <T>(promise: Promise<T>, timeoutMs: number = 8000): Promise<T> => {
      return Promise.race([
        promise,
        new Promise<T>((_, reject) =>
          setTimeout(() => reject(new Error('Request timeout')), timeoutMs)
        )
      ]);
    };

    // Fetch basic repo info first (required)
    const {data: repoInfo} = await withTimeout(
      octokit.request(`GET /repos/${owner}/${repo}`),
      5000
    );

    // Fetch additional data with error handling (optional)
    const [languageResult, commitsResult, contributorsResult, contentResult] = await Promise.allSettled([
      withTimeout(octokit.request(`GET /repos/${owner}/${repo}/languages`), 5000),
      withTimeout(octokit.request(`GET /repos/${owner}/${repo}/commits`), 5000),
      withTimeout(octokit.request(`GET /repos/${owner}/${repo}/contributors`), 5000),
      withTimeout(octokit.request(`GET /repos/${owner}/${repo}/contents`), 5000)
    ]);

    // Extract data safely with fallbacks
    const language = languageResult.status === 'fulfilled' ? languageResult.value.data : {};
    const commits = commitsResult.status === 'fulfilled' ? commitsResult.value.data : [];
    const contributors = contributorsResult.status === 'fulfilled' ? contributorsResult.value.data : [];
    const content = contentResult.status === 'fulfilled' ? contentResult.value.data : [];

    return {
      name: repoInfo.name,
      description: repoInfo.description,
      language: language,
      commits: commits,
      contributors: contributors,
      content: content,
      stars: repoInfo.stargazers_count,
      forks: repoInfo.forks_count,
      url: repoInfo.html_url,
      created_at: repoInfo.created_at,
      updated_at: repoInfo.updated_at
    };
  } catch (error: any) {
    throw new Error(`Failed to fetch repository data: ${error.message}`);
  }
}

export const createServer = (): Express => {
  const app = express();
  app
    .disable("x-powered-by")
    .use(morgan("dev"))
    .use(urlencoded({ extended: true }))
    .use(json())
    .use(cors())
    .get("/status", (_, res) => {
      return res.json({ ok: true });
    })
    .post("/api/repo", async (req, res) => {
      try {
        const { repoUrl } = req.body;
        if (!repoUrl) {
          return res.status(400).json({ error: "Repository URL is required" });
        }
        const repoData = await getRepoDetail(repoUrl);
        return res.status(200).json(repoData);
      } catch (error: any) {
        return res.status(500).json({ error: error.message });
      }
    });

  return app;
};
